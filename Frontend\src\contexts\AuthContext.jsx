import { createContext, useContext, useState, useEffect } from "react"
import axios from "axios"

const AuthContext = createContext()

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

const api = axios.create({
  baseURL: "http://localhost:5000/api",
  headers: {
    "Content-Type": "application/json",
  },
})

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [token, setToken] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const storedToken = localStorage.getItem("token")
    const storedUser = localStorage.getItem("user")

    if (storedToken && storedUser) {
      setToken(storedToken)
      setUser(JSON.parse(storedUser))
      api.defaults.headers.common["Authorization"] = `Bearer ${storedToken}`
    }
    setLoading(false)
  }, [])

  const login = async (username, password, type) => {
    try {
      let response

      if (type === "admin") {
        try {
          response = await api.post("/auth/superadmin/login", { username, password })
        } catch (superAdminError) {
          response = await api.post("/auth/admin/login", { username, password })
        }
      } else {
        response = await api.post("/auth/employee/login", { username, password })
      }

      if (response.data.success) {
        const { token: newToken, user: newUser } = response.data
        setToken(newToken)
        setUser(newUser)
        localStorage.setItem("token", newToken)
        localStorage.setItem("user", JSON.stringify(newUser))
        api.defaults.headers.common["Authorization"] = `Bearer ${newToken}`
        return response.data
      }
      throw response.data
    } catch (error) {
      console.error("Login error:", error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || "Login failed",
      }
    }
  }

  const register = async (formData) => {
    try {
      const response = await api.post("/auth/employee/register", formData)
      return response.data
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || "Registration failed",
      }
    }
  }

  const verifyEmployeeEmail = async (verificationData) => {
    try {
      const response = await api.post("/auth/employee/verify-email", verificationData)
      return response.data
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || "Email verification failed",
      }
    }
  }

  const logout = () => {
    setUser(null)
    setToken(null)
    localStorage.removeItem("token")
    localStorage.removeItem("user")
    delete api.defaults.headers.common["Authorization"]
  }

  const fetchDepartments = async () => {
    try {
      const response = await api.get("/department/all")
      return response.data
    } catch (error) {
      console.error("Error fetching departments:", error)
      return { success: false, data: [] }
    }
  }

  const createDepartment = async (departmentData) => {
    try {
      const response = await api.post("/department/create", departmentData)
      return response.data
    } catch (error) {
      console.error("Error creating department:", error)
      return {
        success: false,
        message: error.response?.data?.message || "Failed to create department",
      }
    }
  }

  const updateDepartment = async (departmentId, departmentData) => {
    try {
      const response = await api.put(`/department/${departmentId}`, departmentData)
      return response.data
    } catch (error) {
      console.error("Error updating department:", error)
      return {
        success: false,
        message: error.response?.data?.message || "Failed to update department",
      }
    }
  }

  const toggleDepartmentStatus = async (departmentId) => {
    try {
      const response = await api.put(`/department/${departmentId}/toggle-status`)
      return response.data
    } catch (error) {
      console.error("Error toggling department status:", error)
      return {
        success: false,
        message: error.response?.data?.message || "Failed to toggle department status",
      }
    }
  }

  const fetchAdmins = async () => {
    try {
      const response = await api.get("/admin/all")
      return response.data
    } catch (error) {
      console.error("Error fetching admins:", error)
      return { success: false, data: [] }
    }
  }

  const createAdmin = async (adminData) => {
    try {
      const response = await api.post("/admin/create", adminData)
      return response.data
    } catch (error) {
      console.error("Error creating admin:", error)
      return {
        success: false,
        message: error.response?.data?.message || "Failed to create admin",
      }
    }
  }

  const updateAdminPassword = async (adminId, passwordData) => {
    try {
      const response = await api.put(`/admin/${adminId}/password`, passwordData)
      return response.data
    } catch (error) {
      console.error("Error updating admin password:", error)
      return {
        success: false,
        message: error.response?.data?.message || "Failed to update admin password",
      }
    }
  }

  const toggleAdminStatus = async (adminId) => {
    try {
      const response = await api.put(`/admin/${adminId}/toggle-status`)
      return response.data
    } catch (error) {
      console.error("Error toggling admin status:", error)
      return {
        success: false,
        message: error.response?.data?.message || "Failed to toggle admin status",
      }
    }
  }

  const value = {
    user,
    token,
    login,
    register,
    verifyEmployeeEmail,
    logout,
    loading,
    fetchDepartments,
    createDepartment,
    updateDepartment,
    toggleDepartmentStatus,
    fetchAdmins,
    createAdmin,
    updateAdminPassword,
    toggleAdminStatus,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
