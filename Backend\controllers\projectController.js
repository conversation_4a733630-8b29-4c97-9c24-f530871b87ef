import Project from "../models/Project.js"
import Employee from "../models/Employee.js"
import Department from "../models/Department.js"

// Create a new project (Admin only)
export const createProject = async (req, res) => {
  try {
    const { name, description, projectType, assignment, priority, sectors, notes, startDate, endDate } = req.body

    // Validate required fields
    if (!name || !projectType) {
      return res.status(400).json({
        success: false,
        message: "Project name and type are required",
      })
    }

    // Validate project type
    if (!["public", "private"].includes(projectType)) {
      return res.status(400).json({
        success: false,
        message: "Project type must be either 'public' or 'private'",
      })
    }

    // Validate priority
    if (priority && !["urgent", "high", "medium", "low"].includes(priority)) {
      return res.status(400).json({
        success: false,
        message: "Priority must be one of: urgent, high, medium, low",
      })
    }

    // Validate sectors if provided
    if (sectors && !["Government", "BFSI", "Defense", "Enterprise", "Telco"].includes(sectors)) {
      return res.status(400).json({
        success: false,
        message: "Sectors must be one of: Government, BFSI, Defense, Enterprise, Telco",
      })
    }

    // Check if sectors is required for Sales and Presales departments
    const userDepartmentName = req.user.department.name
    if ((userDepartmentName === "Sales" || userDepartmentName === "Presales") && !sectors) {
      return res.status(400).json({
        success: false,
        message: "Sectors field is required for Sales and Presales departments",
      })
    }

    // Create project
    const project = new Project({
      name,
      description,
      projectType,
      priority: priority || "medium",
      sectors: sectors || undefined,
      notes,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      createdBy: req.user.id,
      department: req.user.department._id,
      assignment: [],
    })

    // Process employee assignments if provided
    if (assignment && Array.isArray(assignment) && assignment.length > 0) {
      for (const emp of assignment) {
        if (!emp.employee || !emp.department) {
          return res.status(400).json({
            success: false,
            message: "Each assignment must have employee and department",
          })
        }

        // Verify employee exists and belongs to the specified department
        const employee = await Employee.findById(emp.employee)
        if (!employee) {
          return res.status(400).json({
            success: false,
            message: `Employee with ID ${emp.employee} not found`,
          })
        }

        // For assignment, use the employee's actual department
        const actualDepartment = employee.department.toString()

        // For private projects, ensure employee is from admin's department
        if (projectType === "private" && actualDepartment !== req.user.department._id.toString()) {
          return res.status(400).json({
            success: false,
            message: "Private projects can only assign employees from your department",
          })
        }

        project.assignment.push({
          employee: emp.employee,
          department: projectType === "private" ? req.user.department._id : actualDepartment,
        })
      }
    }

    await project.save()

    // Populate the project with employee and department details
    await project.populate([
      {
        path: "assignment.employee",
        select: "name email username",
      },
      {
        path: "assignment.department",
        select: "name",
      },
      {
        path: "createdBy",
        select: "name email",
      },
      {
        path: "department",
        select: "name",
      },
    ])

    res.status(201).json({
      success: true,
      message: "Project created successfully",
      project,
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

// Get all projects for admin (filtered by department for admins)
export const getAllProjects = async (req, res) => {
  try {
    const { status, priority, projectType, sectors, page = 1, limit = 10 } = req.query

    // Build filter based on user role
    const filter = { isActive: true }

    // Admins can only see projects they created or projects from their department
    if (req.user.role === "admin") {
      filter.$or = [{ createdBy: req.user.id }, { department: req.user.department }]
    }

    // Apply additional filters
    if (status) filter.status = status
    if (priority) filter.priority = priority
    if (projectType) filter.projectType = projectType
    if (sectors) filter.sectors = sectors

    const skip = (Number.parseInt(page) - 1) * Number.parseInt(limit)

    const projects = await Project.find(filter)
      .populate([
        {
          path: "assignment.employee",
          select: "name email username",
        },
        {
          path: "assignment.department",
          select: "name",
        },
        {
          path: "createdBy",
          select: "name email",
        },
        {
          path: "department",
          select: "name",
        },
      ])
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(Number.parseInt(limit))

    const total = await Project.countDocuments(filter)

    res.json({
      success: true,
      projects,
      pagination: {
        current: Number.parseInt(page),
        pages: Math.ceil(total / Number.parseInt(limit)),
        total,
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

// Get project by ID
export const getProjectById = async (req, res) => {
  try {
    const { projectId } = req.params

    const project = await Project.findById(projectId).populate([
      {
        path: "assignment.employee",
        select: "name email username contactNumber",
      },
      {
        path: "assignment.department",
        select: "name description",
      },
      {
        path: "createdBy",
        select: "name email",
      },
      {
        path: "department",
        select: "name description",
      },
    ])

    if (!project) {
      return res.status(404).json({
        success: false,
        message: "Project not found",
      })
    }

    // Check if admin has access to this project
    if (req.user.role === "admin") {
      const hasAccess =
        project.createdBy._id.toString() === req.user.id ||
        project.department._id.toString() === req.user.department.toString()

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: "Access denied",
        })
      }
    }

    res.json({
      success: true,
      project,
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

// Update project
export const updateProject = async (req, res) => {
  try {
    const { projectId } = req.params
    const updates = req.body

    const project = await Project.findById(projectId)

    if (!project) {
      return res.status(404).json({
        success: false,
        message: "Project not found",
      })
    }

    // Check if admin has permission to update this project
    if (req.user.role === "admin" && project.createdBy.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: "You can only update projects you created",
      })
    }

    // Validate updates
    if (updates.projectType && !["public", "private"].includes(updates.projectType)) {
      return res.status(400).json({
        success: false,
        message: "Project type must be either 'public' or 'private'",
      })
    }

    if (updates.priority && !["urgent", "high", "medium", "low"].includes(updates.priority)) {
      return res.status(400).json({
        success: false,
        message: "Priority must be one of: urgent, high, medium, low",
      })
    }

    if (updates.status && !["active", "completed", "cancelled", "on-hold"].includes(updates.status)) {
      return res.status(400).json({
        success: false,
        message: "Status must be one of: active, completed, cancelled, on-hold",
      })
    }

    // Validate sectors if provided
    if (updates.sectors && !["Government", "BFSI", "Defense", "Enterprise", "Telco"].includes(updates.sectors)) {
      return res.status(400).json({
        success: false,
        message: "Sectors must be one of: Government, BFSI, Defense, Enterprise, Telco",
      })
    }

    // Check if sectors is required for Sales and Presales departments
    const userDepartmentName = req.user.department.name
    if (
      (userDepartmentName === "Sales" || userDepartmentName === "Presales") &&
      updates.sectors === undefined &&
      !project.sectors
    ) {
      return res.status(400).json({
        success: false,
        message: "Sectors field is required for Sales and Presales departments",
      })
    }

    // Handle assignment updates
    if (updates.assignment) {
      // Validate and process new assignments
      const newAssignments = []

      for (const emp of updates.assignment) {
        if (!emp.employee || !emp.department) {
          return res.status(400).json({
            success: false,
            message: "Each assignment must have employee and department",
          })
        }

        // Verify employee exists and belongs to the specified department
        const employee = await Employee.findById(emp.employee)
        if (!employee) {
          return res.status(400).json({
            success: false,
            message: `Employee with ID ${emp.employee} not found`,
          })
        }

        // For assignment, use the employee's actual department, not the one specified in the request
        const actualDepartment = employee.department.toString()

        // For private projects, ensure employee is from admin's department
        const projectType = updates.projectType || project.projectType
        if (projectType === "private" && actualDepartment !== req.user.department._id.toString()) {
          return res.status(400).json({
            success: false,
            message: "Private projects can only assign employees from your department",
          })
        }

        newAssignments.push({
          employee: emp.employee,
          department: projectType === "private" ? req.user.department._id : actualDepartment,
        })
      }

      updates.assignment = newAssignments
    }

    // Update completion date if status is being set to completed
    if (updates.status === "completed" && project.status !== "completed") {
      updates.completedDate = new Date()
    }

    // Apply updates
    Object.assign(project, updates)
    await project.save()

    // Populate the updated project
    await project.populate([
      {
        path: "assignment.employee",
        select: "name email username",
      },
      {
        path: "assignment.department",
        select: "name",
      },
      {
        path: "createdBy",
        select: "name email",
      },
      {
        path: "department",
        select: "name",
      },
    ])

    res.json({
      success: true,
      message: "Project updated successfully",
      project,
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

// Delete project (soft delete)
export const deleteProject = async (req, res) => {
  try {
    const { projectId } = req.params

    const project = await Project.findById(projectId)

    if (!project) {
      return res.status(404).json({
        success: false,
        message: "Project not found",
      })
    }

    // Check if admin has permission to delete this project
    if (req.user.role === "admin" && project.createdBy.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: "You can only delete projects you created",
      })
    }

    // Soft delete
    project.isActive = false
    await project.save()

    res.json({
      success: true,
      message: "Project deleted successfully",
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

// Get employees by department (for assignment)
export const getEmployeesByDepartment = async (req, res) => {
  try {
    const { departmentId } = req.params
    const { user } = req

    // Authorization logic:
    // - SuperAdmins can access any department
    // - Admins can access their own department for private projects
    // - Admins can access any department for public projects (cross-department assignment)
    if (user.role === "admin" && user.department.toString() !== departmentId) {
      // Allow admins to access other departments for public project assignments
      // This is necessary for cross-department collaboration in public projects
      console.log(`Admin ${user.id} accessing department ${departmentId} for public project assignment`)
    }

    // Verify department exists
    const department = await Department.findById(departmentId)
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Department not found",
      })
    }

    // Get active employees from the department
    const employees = await Employee.find({
      department: departmentId,
      isActive: true,
      isEmailVerified: true,
    }).select("name email username contactNumber department")

    res.json({
      success: true,
      employees,
      department: {
        id: department._id,
        name: department.name,
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}
