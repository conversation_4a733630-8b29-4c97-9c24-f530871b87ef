import User from "../models/User.js"

console.log("🔥 ADMIN CONTROLLER LOADED")

export const createAdmin = async (req, res) => {
  try {
    const { name, email, username, password, department } = req.body

    const existingUser = await User.findOne({
      $or: [{ email }, { username }],
    })

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: "User with this email or username already exists",
      })
    }

    // Check if department already has an admin assigned
    const existingDepartmentAdmin = await User.findOne({
      department: department,
      role: "admin",
      isActive: true,
    })

    if (existingDepartmentAdmin) {
      return res.status(400).json({
        success: false,
        message: "This department already has an admin assigned",
      })
    }

    const admin = new User({
      name,
      email,
      username,
      password,
      role: "admin",
      department,
      createdBy: req.user.id,
    })

    await admin.save()
    await admin.populate("department")

    res.status(201).json({
      success: true,
      message: "Admin created successfully",
      admin: {
        id: admin._id,
        name: admin.name,
        email: admin.email,
        username: admin.username,
        role: admin.role,
        department: admin.department,
        isActive: admin.isActive,
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

export const getAllAdmins = async (req, res) => {
  try {
    const admins = await User.find({ role: "admin" }).populate("department").select("-password").sort({ createdAt: -1 })

    res.json({
      success: true,
      data: admins,
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

export const updateAdminPassword = async (req, res) => {
  try {
    const { adminId } = req.params
    const { password } = req.body

    const admin = await User.findOne({ _id: adminId, role: "admin" })
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: "Admin not found",
      })
    }

    admin.password = password
    await admin.save()

    res.json({
      success: true,
      message: "Admin password updated successfully",
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

export const toggleAdminStatus = async (req, res) => {
  try {
    const { adminId } = req.params

    const admin = await User.findOne({ _id: adminId, role: "admin" })
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: "Admin not found",
      })
    }

    admin.isActive = !admin.isActive
    await admin.save()

    res.json({
      success: true,
      message: `Admin ${admin.isActive ? "activated" : "deactivated"} successfully`,
      admin: {
        id: admin._id,
        name: admin.name,
        isActive: admin.isActive,
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}
