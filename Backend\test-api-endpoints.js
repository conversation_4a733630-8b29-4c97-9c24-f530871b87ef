// Quick test to verify API endpoints return consistent data format
import axios from 'axios'

const BASE_URL = 'http://localhost:5000/api'

// Test with a mock token (this will fail auth but we can see response structure)
const testEndpoints = async () => {
  console.log('🧪 Testing API endpoint response formats...\n')
  
  const endpoints = [
    { name: 'Admin All', url: '/admin/all' },
    { name: 'Department All', url: '/department/all' },
    { name: 'Employee All', url: '/employee/all' },
    { name: 'Project All', url: '/project/all' },
    { name: 'TaskType All', url: '/tasktype/all' }
  ]
  
  for (const endpoint of endpoints) {
    try {
      console.log(`📡 Testing ${endpoint.name}: ${endpoint.url}`)
      const response = await axios.get(`${BASE_URL}${endpoint.url}`, {
        headers: { Authorization: 'Bearer invalid-token' }
      })
      
      // This shouldn't happen with invalid token, but if it does:
      console.log(`✅ Response structure:`, Object.keys(response.data))
      
    } catch (error) {
      if (error.response) {
        console.log(`🔒 Expected auth error (${error.response.status}): ${error.response.data.message}`)
      } else {
        console.log(`❌ Network error: ${error.message}`)
      }
    }
    console.log('')
  }
  
  // Test department endpoint (no auth required)
  try {
    console.log('📡 Testing Department All (no auth):')
    const response = await axios.get(`${BASE_URL}/department/all`)
    console.log(`✅ Response structure:`, Object.keys(response.data))
    if (response.data.success) {
      console.log(`✅ Has 'data' property: ${response.data.data ? 'Yes' : 'No'}`)
      console.log(`✅ Data is array: ${Array.isArray(response.data.data) ? 'Yes' : 'No'}`)
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`)
  }
}

testEndpoints()
