import Department from "../models/Department.js"

export const createDepartment = async (req, res) => {
  try {
    const { name, description } = req.body

    const existingDepartment = await Department.findOne({ name })
    if (existingDepartment) {
      return res.status(400).json({
        success: false,
        message: "Department with this name already exists",
      })
    }

    const department = new Department({
      name,
      description,
      createdBy: req.user.id,
    })

    await department.save()

    res.status(201).json({
      success: true,
      message: "Department created successfully",
      department,
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

export const getAllDepartments = async (req, res) => {
  try {
    const departments = await Department.find({ isActive: true }).populate("createdBy", "name").sort({ createdAt: -1 })

    res.json({
      success: true,
      data: departments,
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

export const updateDepartment = async (req, res) => {
  try {
    const { departmentId } = req.params
    const { name, description } = req.body

    const department = await Department.findById(departmentId)
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Department not found",
      })
    }

    if (name !== department.name) {
      const existingDepartment = await Department.findOne({ name })
      if (existingDepartment) {
        return res.status(400).json({
          success: false,
          message: "Department name already exists",
        })
      }
    }

    department.name = name || department.name
    department.description = description || department.description

    await department.save()

    res.json({
      success: true,
      message: "Department updated successfully",
      department,
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

export const toggleDepartmentStatus = async (req, res) => {
  try {
    const { departmentId } = req.params

    const department = await Department.findById(departmentId)
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Department not found",
      })
    }

    department.isActive = !department.isActive
    await department.save()

    res.json({
      success: true,
      message: `Department ${department.isActive ? "activated" : "deactivated"} successfully`,
      department,
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}
