"use client"

import { useState, useEffect } from "react"
import { useAuth } from "../contexts/AuthContext"

export default function AdminManagement({ onClose }) {
  const { fetchAdmins, createAdmin, toggleAdminStatus, fetchDepartments, user } = useAuth()
  const [admins, setAdmins] = useState([])
  const [departments, setDepartments] = useState([])
  const [availableDepartments, setAvailableDepartments] = useState([])
  const [loading, setLoading] = useState(true)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    username: "",
    password: "",
    confirmPassword: "",
    department: "",
  })
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  const canWrite = user?.role === "superadmin" && user?.permissions === "read-write"

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    setLoading(true)
    const [adminsResult, departmentsResult] = await Promise.all([fetchAdmins(), fetchDepartments()])

    if (adminsResult.success) {
      setAdmins(adminsResult.data || [])
    } else {
      setError("Failed to load admins")
    }

    if (departmentsResult.success) {
      const activeDepartments = (departmentsResult.data || []).filter((dept) => dept.isActive)
      setDepartments(activeDepartments)

      // Filter out departments that already have active admins
      if (adminsResult.success) {
        const occupiedDepartmentIds = (adminsResult.data || [])
          .filter((admin) => admin.isActive && admin.department)
          .map((admin) => admin.department._id)

        const available = activeDepartments.filter((dept) => !occupiedDepartmentIds.includes(dept._id))
        setAvailableDepartments(available)
      }
    }

    setLoading(false)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError("")
    setSuccess("")

    if (!canWrite) {
      setError("You don't have permission to perform this action")
      return
    }

    // Validation
    if (
      !formData.name.trim() ||
      !formData.email.trim() ||
      !formData.username.trim() ||
      !formData.password.trim() ||
      !formData.department
    ) {
      setError("All fields are required")
      return
    }

    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match")
      return
    }

    if (formData.password.length < 6) {
      setError("Password must be at least 6 characters long")
      return
    }

    try {
      const result = await createAdmin({
        name: formData.name,
        email: formData.email,
        username: formData.username,
        password: formData.password,
        department: formData.department,
      })

      if (result.success) {
        setSuccess("Admin created successfully")
        setFormData({
          name: "",
          email: "",
          username: "",
          password: "",
          confirmPassword: "",
          department: "",
        })
        setShowCreateForm(false)
        loadData()
      } else {
        setError(result.message)
      }
    } catch (error) {
      setError("An error occurred while creating the admin")
    }
  }

  const handleToggleStatus = async (adminId) => {
    if (!canWrite) {
      setError("You don't have permission to toggle admin status")
      return
    }

    const result = await toggleAdminStatus(adminId)
    if (result.success) {
      setSuccess("Admin status updated successfully")
      loadData()
    } else {
      setError(result.message)
    }
  }

  const resetForm = () => {
    setFormData({
      name: "",
      email: "",
      username: "",
      password: "",
      confirmPassword: "",
      department: "",
    })
    setShowCreateForm(false)
    setError("")
    setSuccess("")
  }

  const getDepartmentStatus = (departmentId) => {
    const admin = admins.find((admin) => admin.isActive && admin.department && admin.department._id === departmentId)
    return admin ? { occupied: true, adminName: admin.name } : { occupied: false }
  }

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-gray-900 dark:text-white">Loading admins...</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Admin Management</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Error/Success Messages */}
          {error && (
            <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-red-600 dark:text-red-400">{error}</p>
            </div>
          )}
          {success && (
            <div className="mb-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
              <p className="text-green-600 dark:text-green-400">{success}</p>
            </div>
          )}

          {/* Department Status Overview */}
          <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <h3 className="text-lg font-medium text-blue-900 dark:text-blue-100 mb-3">Department Status</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {departments.map((dept) => {
                const status = getDepartmentStatus(dept._id)
                return (
                  <div
                    key={dept._id}
                    className={`p-3 rounded-lg border ${
                      status.occupied
                        ? "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800"
                        : "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800"
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-gray-900 dark:text-white">{dept.name}</span>
                      <span
                        className={`text-xs px-2 py-1 rounded-full ${
                          status.occupied
                            ? "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                            : "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                        }`}
                      >
                        {status.occupied ? "Occupied" : "Available"}
                      </span>
                    </div>
                    {status.occupied && (
                      <p className="text-sm text-red-600 dark:text-red-400 mt-1">Admin: {status.adminName}</p>
                    )}
                  </div>
                )
              })}
            </div>
          </div>

          {/* Create Admin Button */}
          {canWrite && !showCreateForm && (
            <div className="mb-6">
              <button
                onClick={() => setShowCreateForm(true)}
                disabled={availableDepartments.length === 0}
                className={`${
                  availableDepartments.length === 0 ? "bg-gray-400 cursor-not-allowed" : "bg-blue-600 hover:bg-blue-700"
                } text-white px-4 py-2 rounded-lg flex items-center space-x-2`}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                <span>Create Admin</span>
              </button>
              {availableDepartments.length === 0 && (
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                  All departments already have admins assigned
                </p>
              )}
            </div>
          )}

          {/* Create Form */}
          {showCreateForm && (
            <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Create New Admin</h3>
              <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Full Name *</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                    placeholder="Enter full name"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email *</label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                    placeholder="Enter email address"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Username *</label>
                  <input
                    type="text"
                    value={formData.username}
                    onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                    placeholder="Enter username"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Available Department *
                  </label>
                  <select
                    value={formData.department}
                    onChange={(e) => setFormData({ ...formData, department: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                    required
                  >
                    <option value="">Select Available Department</option>
                    {availableDepartments.map((dept) => (
                      <option key={dept._id} value={dept._id}>
                        {dept.name}
                      </option>
                    ))}
                  </select>
                  {availableDepartments.length === 0 && (
                    <p className="text-sm text-red-600 dark:text-red-400 mt-1">
                      No departments available. All departments already have admins assigned.
                    </p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Password *</label>
                  <input
                    type="password"
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                    placeholder="Enter password"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Confirm Password *
                  </label>
                  <input
                    type="password"
                    value={formData.confirmPassword}
                    onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                    placeholder="Confirm password"
                    required
                  />
                </div>
                <div className="md:col-span-2 flex space-x-3">
                  <button
                    type="submit"
                    disabled={availableDepartments.length === 0}
                    className={`${
                      availableDepartments.length === 0
                        ? "bg-gray-400 cursor-not-allowed"
                        : "bg-blue-600 hover:bg-blue-700"
                    } text-white px-4 py-2 rounded-lg`}
                  >
                    Create Admin
                  </button>
                  <button
                    type="button"
                    onClick={resetForm}
                    className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Admins List */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Administrators</h3>
            {admins.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400">No administrators found.</p>
            ) : (
              <div className="grid gap-4">
                {admins.map((admin) => (
                  <div
                    key={admin._id}
                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h4 className="text-lg font-medium text-gray-900 dark:text-white">{admin.name}</h4>
                        <div className="text-gray-600 dark:text-gray-400 mt-1 space-y-1">
                          <p>Email: {admin.email}</p>
                          <p>Username: {admin.username}</p>
                          <p>Department: {admin.department?.name || "N/A"}</p>
                        </div>
                        <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500 dark:text-gray-400">
                          <span
                            className={`px-2 py-1 rounded-full text-xs ${
                              admin.isActive
                                ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                                : "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                            }`}
                          >
                            {admin.isActive ? "Active" : "Inactive"}
                          </span>
                          <span>Created: {new Date(admin.createdAt).toLocaleDateString()}</span>
                        </div>
                      </div>
                      {canWrite && (
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleToggleStatus(admin._id)}
                            className={`${
                              admin.isActive
                                ? "text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                                : "text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"
                            }`}
                            title={admin.isActive ? "Deactivate Admin" : "Activate Admin"}
                          >
                            {admin.isActive ? (
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636"
                                />
                              </svg>
                            ) : (
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                              </svg>
                            )}
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
