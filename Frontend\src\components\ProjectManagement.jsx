"use client"

import { useState, useEffect } from "react"
import { useAuth } from "../contexts/AuthContext"
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent } from "./ui/Card"
import { Button } from "./ui/Button"
import { Input } from "./ui/Input"
import { Select } from "./ui/Select"
import { Checkbox } from "./ui/Checkbox"
import { MultiSelect } from "./ui/MultiSelect"
import { Badge } from "./ui/Badge"

export default function ProjectManagement({ onClose }) {
  const { user } = useAuth()
  const [projects, setProjects] = useState([])
  const [departments, setDepartments] = useState([])
  const [loading, setLoading] = useState(true)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingProject, setEditingProject] = useState(null)
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    projectType: "private",
    priority: "medium",
    sectors: "",
    notes: "",
    startDate: "",
    endDate: "",
    assignment: [],
  })
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [selectedDepartment, setSelectedDepartment] = useState("")
  const [departmentEmployees, setDepartmentEmployees] = useState([])
  const [loadingEmployees, setLoadingEmployees] = useState(false)

  // Check if user is from Sales or Presales department
  const isSalesOrPresales = user?.department?.name === "Sales" || user?.department?.name === "Presales"

  // Sectors options
  const sectorsOptions = [
    { value: "Government", label: "Government" },
    { value: "BFSI", label: "BFSI" },
    { value: "Defense", label: "Defense" },
    { value: "Enterprise", label: "Enterprise" },
    { value: "Telco", label: "Telco" },
  ]

  useEffect(() => {
    loadData()
  }, [])

  // Load employees for private projects (admin's department)
  useEffect(() => {
    if (formData.projectType === "private" && user?.department?._id) {
      loadEmployeesByDepartment(user.department._id)
    }
  }, [formData.projectType, user?.department?._id])

  const loadData = async () => {
    try {
      setLoading(true)
      await Promise.all([loadProjects(), loadDepartments()])
    } catch (error) {
      setError("Failed to load data")
    } finally {
      setLoading(false)
    }
  }

  const loadProjects = async () => {
    try {
      const token = localStorage.getItem("token")
      const response = await fetch("http://localhost:5000/api/project/all", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      const data = await response.json()
      if (data.success) {
        setProjects(data.data || [])
      } else {
        throw new Error(data.message)
      }
    } catch (error) {
      console.error("Error loading projects:", error)
      throw error
    }
  }

  const loadDepartments = async () => {
    try {
      const response = await fetch("http://localhost:5000/api/department/all")
      const data = await response.json()
      if (data.success) {
        setDepartments(data.data || [])
      } else {
        throw new Error(data.message)
      }
    } catch (error) {
      console.error("Error loading departments:", error)
      throw error
    }
  }

  const loadEmployeesByDepartment = async (departmentId) => {
    try {
      setLoadingEmployees(true)
      setError("") // Clear previous errors
      const token = localStorage.getItem("token")

      const response = await fetch(`http://localhost:5000/api/project/employees/department/${departmentId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      const data = await response.json()

      if (data.success) {
        setDepartmentEmployees(data.data || [])
      } else {
        throw new Error(data.message)
      }
    } catch (error) {
      console.error("Error loading employees:", error)
      setError(`Failed to load employees: ${error.message}`)
      setDepartmentEmployees([]) // Clear employees on error
    } finally {
      setLoadingEmployees(false)
    }
  }

  const handleDepartmentChange = (departmentId) => {
    setSelectedDepartment(departmentId)
    if (departmentId) {
      loadEmployeesByDepartment(departmentId)
    } else {
      setDepartmentEmployees([])
    }
  }

  const handleEmployeeSelection = (selectedEmployeeIds) => {
    if (formData.projectType === "public") {
      // For public projects, add new employees from selected department
      // Use the selected department as the department for these employees
      const newAssignments = selectedEmployeeIds.map((employeeId) => {
        // Find the employee to get their actual department
        const employee = departmentEmployees.find((emp) => emp._id === employeeId)
        return {
          employee: employeeId,
          department: selectedDepartment, // Use selected department for assignment tracking
        }
      })

      // Remove existing assignments from this department and add new ones
      const existingFromOtherDepts = formData.assignment.filter((a) => a.department !== selectedDepartment)

      setFormData({
        ...formData,
        assignment: [...existingFromOtherDepts, ...newAssignments],
      })
    } else {
      // For private projects, replace all assignments with selected employees from admin's department
      const newAssignments = selectedEmployeeIds.map((employeeId) => ({
        employee: employeeId,
        department: user?.department?._id,
      }))
      setFormData({
        ...formData,
        assignment: newAssignments,
      })
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError("")
    setSuccess("")

    // Validate sectors for Sales and Presales departments
    if (isSalesOrPresales && !formData.sectors) {
      setError("Sectors field is required for Sales and Presales departments")
      return
    }

    try {
      const token = localStorage.getItem("token")
      const url = editingProject
        ? `http://localhost:5000/api/project/${editingProject._id}`
        : "http://localhost:5000/api/project/create"

      const method = editingProject ? "PUT" : "POST"

      // Prepare form data - only include sectors if user is from Sales or Presales
      const submitData = { ...formData }
      if (!isSalesOrPresales) {
        delete submitData.sectors
      }

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(submitData),
      })

      const data = await response.json()
      if (data.success) {
        setSuccess(editingProject ? "Project updated successfully" : "Project created successfully")
        resetForm()
        loadProjects()
      } else {
        setError(data.message)
      }
    } catch (error) {
      setError("An error occurred. Please try again.")
    }
  }

  const handleEdit = (project) => {
    setEditingProject(project)

    // Convert assignment objects to the format expected by the form
    const formattedAssignment = (project.assignment || []).map((assignment) => ({
      employee: assignment.employee._id || assignment.employee,
      department: assignment.department._id || assignment.department,
    }))

    setFormData({
      name: project.name,
      description: project.description || "",
      projectType: project.projectType,
      priority: project.priority,
      sectors: project.sectors || "",
      notes: project.notes || "",
      startDate: project.startDate ? project.startDate.split("T")[0] : "",
      endDate: project.endDate ? project.endDate.split("T")[0] : "",
      assignment: formattedAssignment,
    })
    setShowCreateForm(true)
  }

  const handleDelete = async (projectId) => {
    if (!window.confirm("Are you sure you want to delete this project?")) {
      return
    }

    try {
      const token = localStorage.getItem("token")
      const response = await fetch(`http://localhost:5000/api/project/${projectId}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      const data = await response.json()
      if (data.success) {
        setSuccess("Project deleted successfully")
        loadProjects()
      } else {
        setError(data.message)
      }
    } catch (error) {
      setError("Failed to delete project")
    }
  }

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      projectType: "private",
      priority: "medium",
      sectors: "",
      notes: "",
      startDate: "",
      endDate: "",
      assignment: [],
    })
    setEditingProject(null)
    setShowCreateForm(false)
    setSelectedDepartment("")
    setDepartmentEmployees([])
    setError("")
    setSuccess("")
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "urgent":
        return "danger"
      case "high":
        return "warning"
      case "medium":
        return "primary"
      case "low":
        return "success"
      default:
        return "default"
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "success"
      case "completed":
        return "primary"
      case "cancelled":
        return "danger"
      case "on-hold":
        return "warning"
      default:
        return "default"
    }
  }

  const getSectorColor = (sector) => {
    switch (sector) {
      case "Government":
        return "primary"
      case "BFSI":
        return "success"
      case "Defense":
        return "danger"
      case "Enterprise":
        return "warning"
      case "Telco":
        return "info"
      default:
        return "default"
    }
  }

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600 dark:text-gray-400">Loading projects...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Project Management</h2>
            <p className="text-gray-600 dark:text-gray-400">Create and manage department projects</p>
          </div>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Success/Error Messages */}
          {success && (
            <div className="mb-4 p-4 bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 rounded-lg">
              {success}
            </div>
          )}
          {error && (
            <div className="mb-4 p-4 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 rounded-lg">{error}</div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {showCreateForm ? (editingProject ? "Edit Project" : "Create New Project") : "Projects"}
            </h3>
            {!showCreateForm && (
              <Button onClick={() => setShowCreateForm(true)}>
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Create Project
              </Button>
            )}
          </div>

          {/* Create/Edit Form */}
          {showCreateForm && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>{editingProject ? "Edit Project" : "Create New Project"}</CardTitle>
                <CardDescription>
                  {editingProject ? "Update project details" : "Fill in the project information"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Project Name */}
                    <Input
                      label="Project Name *"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Enter project name"
                      required
                    />

                    {/* Priority */}
                    <Select
                      label="Priority *"
                      value={formData.priority}
                      onChange={(e) => setFormData({ ...formData, priority: e.target.value })}
                      options={[
                        { value: "urgent", label: "Urgent" },
                        { value: "high", label: "High" },
                        { value: "medium", label: "Medium" },
                        { value: "low", label: "Low" },
                      ]}
                      required
                    />
                  </div>

                  {/* Sectors - Only show for Sales and Presales departments */}
                  {isSalesOrPresales && (
                    <Select
                      label="Sectors *"
                      value={formData.sectors}
                      onChange={(e) => setFormData({ ...formData, sectors: e.target.value })}
                      options={sectorsOptions}
                      placeholder="Select a sector"
                      required
                    />
                  )}

                  {/* Description */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Description
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                      placeholder="Enter project description"
                      rows={3}
                    />
                  </div>

                  {/* Project Type */}
                  <div>
                    <Checkbox
                      label="Public Project"
                      description="Allow assignment of employees from other departments"
                      checked={formData.projectType === "public"}
                      onChange={(e) => {
                        const newProjectType = e.target.checked ? "public" : "private"
                        setFormData({
                          ...formData,
                          projectType: newProjectType,
                          assignment: editingProject ? formData.assignment : [], // Only reset for new projects
                        })
                        // Reset selected department when changing type
                        setSelectedDepartment("")
                        setDepartmentEmployees([])
                      }}
                    />
                  </div>

                  {/* Employee Assignment */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white">Employee Assignment</h4>

                    {/* Show current assignments for editing */}
                    {editingProject && formData.assignment.length > 0 && (
                      <div>
                        <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Current Assignments ({formData.assignment.length} employees)
                        </h5>
                        <div className="flex flex-wrap gap-2 mb-4">
                          {formData.assignment.map((assignment, index) => {
                            const dept = departments.find((d) => d._id === assignment.department)
                            const empName =
                              editingProject.assignment.find(
                                (a) => (a.employee._id || a.employee) === assignment.employee,
                              )?.employee?.name || "Unknown Employee"

                            return (
                              <div
                                key={index}
                                className="inline-flex items-center gap-2 px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-sm rounded-md"
                              >
                                <span>
                                  {empName} ({dept?.name || "Unknown Dept"})
                                </span>
                                <button
                                  type="button"
                                  onClick={() => {
                                    const newAssignments = formData.assignment.filter((_, i) => i !== index)
                                    setFormData({ ...formData, assignment: newAssignments })
                                  }}
                                  className="hover:bg-blue-200 dark:hover:bg-blue-800 rounded-full p-0.5"
                                >
                                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M6 18L18 6M6 6l12 12"
                                    />
                                  </svg>
                                </button>
                              </div>
                            )
                          })}
                        </div>
                      </div>
                    )}

                    {/* Department Selection (only for public projects) */}
                    {formData.projectType === "public" && (
                      <Select
                        label="Select Department to Add Employees"
                        value={selectedDepartment}
                        onChange={(e) => handleDepartmentChange(e.target.value)}
                        options={departments.map((dept) => ({
                          value: dept._id,
                          label: dept.name,
                        }))}
                        placeholder="Choose a department"
                      />
                    )}

                    {/* Employee Selection */}
                    {(formData.projectType === "private" || selectedDepartment) && (
                      <div>
                        {formData.projectType === "private" && (
                          <div className="mb-2">
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              Employees from your department ({user?.department?.name})
                            </p>
                          </div>
                        )}

                        {loadingEmployees ? (
                          <div className="flex items-center justify-center py-4">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                            <span className="ml-2 text-gray-600 dark:text-gray-400">Loading employees...</span>
                          </div>
                        ) : (
                          <MultiSelect
                            label="Select Employees"
                            options={departmentEmployees.map((emp) => ({
                              value: emp._id,
                              label: `${emp.name} (${emp.email})`,
                            }))}
                            value={
                              formData.projectType === "private"
                                ? formData.assignment.map((a) => a.employee)
                                : formData.assignment
                                    .filter((a) => a.department === selectedDepartment)
                                    .map((a) => a.employee)
                            }
                            onChange={handleEmployeeSelection}
                            placeholder="Choose employees to assign"
                          />
                        )}
                      </div>
                    )}
                  </div>

                  {/* Dates */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Input
                      label="Start Date"
                      type="date"
                      value={formData.startDate}
                      onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                    />
                    <Input
                      label="End Date"
                      type="date"
                      value={formData.endDate}
                      onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                    />
                  </div>

                  {/* Notes */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Notes</label>
                    <textarea
                      value={formData.notes}
                      onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                      placeholder="Additional notes or comments"
                      rows={3}
                    />
                  </div>

                  {/* Form Actions */}
                  <div className="flex space-x-3">
                    <Button type="submit">{editingProject ? "Update Project" : "Create Project"}</Button>
                    <Button type="button" variant="secondary" onClick={resetForm}>
                      Cancel
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          )}

          {/* Projects List */}
          {!showCreateForm && (
            <div className="space-y-4">
              {(projects || []).length === 0 ? (
                <Card>
                  <CardContent className="text-center py-8">
                    <svg
                      className="w-12 h-12 text-gray-400 mx-auto mb-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                      />
                    </svg>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Projects Found</h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">Get started by creating your first project.</p>
                    <Button onClick={() => setShowCreateForm(true)}>Create Your First Project</Button>
                  </CardContent>
                </Card>
              ) : (
                (projects || []).map((project) => (
                  <Card key={project._id} className="hover:shadow-lg transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{project.name}</h3>
                            <Badge variant={getPriorityColor(project.priority)}>{project.priority.toUpperCase()}</Badge>
                            <Badge variant={getStatusColor(project.status)}>{project.status.toUpperCase()}</Badge>
                            <Badge variant={project.projectType === "public" ? "primary" : "secondary"}>
                              {project.projectType.toUpperCase()}
                            </Badge>
                            {project.sectors && (
                              <Badge variant={getSectorColor(project.sectors)}>{project.sectors}</Badge>
                            )}
                          </div>

                          {project.description && (
                            <p className="text-gray-600 dark:text-gray-400 mb-3">{project.description}</p>
                          )}

                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="font-medium text-gray-700 dark:text-gray-300">Created by:</span>
                              <p className="text-gray-600 dark:text-gray-400">{project.createdBy?.name}</p>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700 dark:text-gray-300">Department:</span>
                              <p className="text-gray-600 dark:text-gray-400">{project.department?.name}</p>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700 dark:text-gray-300">Assigned Employees:</span>
                              <p className="text-gray-600 dark:text-gray-400">{project.assignment?.length || 0}</p>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700 dark:text-gray-300">Created:</span>
                              <p className="text-gray-600 dark:text-gray-400">
                                {new Date(project.createdAt).toLocaleDateString()}
                              </p>
                            </div>
                          </div>

                          {project.assignment && project.assignment.length > 0 && (
                            <div className="mt-4">
                              <span className="font-medium text-gray-700 dark:text-gray-300 text-sm">Assigned to:</span>
                              <div className="flex flex-wrap gap-2 mt-2">
                                {project.assignment.map((assignment, index) => (
                                  <Badge key={index} variant="outline">
                                    {assignment.employee?.name} ({assignment.department?.name})
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}

                          {project.notes && (
                            <div className="mt-4">
                              <span className="font-medium text-gray-700 dark:text-gray-300 text-sm">Notes:</span>
                              <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">{project.notes}</p>
                            </div>
                          )}
                        </div>

                        <div className="flex space-x-2 ml-4">
                          <Button size="sm" variant="outline" onClick={() => handleEdit(project)}>
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                              />
                            </svg>
                          </Button>
                          <Button size="sm" variant="danger" onClick={() => handleDelete(project._id)}>
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                              />
                            </svg>
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
