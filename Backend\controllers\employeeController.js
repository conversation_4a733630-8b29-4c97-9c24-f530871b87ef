import Employee from "../models/Employee.js"

export const getAllEmployees = async (req, res) => {
  try {
    const employees = await Employee.find().populate("department").select("-password").sort({ createdAt: -1 })

    res.json({
      success: true,
      data: employees,
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

export const getEmployeeById = async (req, res) => {
  try {
    const { employeeId } = req.params

    const employee = await Employee.findById(employeeId).populate("department").select("-password")

    if (!employee) {
      return res.status(404).json({
        success: false,
        message: "Employee not found",
      })
    }

    res.json({
      success: true,
      employee,
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

export const updateEmployeeProfile = async (req, res) => {
  try {
    const { employeeId } = req.params
    const { name, email, contactNumber, address } = req.body

    const employee = await Employee.findById(employeeId)
    if (!employee) {
      return res.status(404).json({
        success: false,
        message: "Employee not found",
      })
    }

    if (email !== employee.email) {
      const existingEmployee = await Employee.findOne({ email })
      if (existingEmployee) {
        return res.status(400).json({
          success: false,
          message: "Email already exists",
        })
      }
    }

    employee.name = name || employee.name
    employee.email = email || employee.email
    employee.contactNumber = contactNumber || employee.contactNumber
    employee.address = address || employee.address

    await employee.save()
    await employee.populate("department")

    res.json({
      success: true,
      message: "Profile updated successfully",
      employee: {
        id: employee._id,
        name: employee.name,
        email: employee.email,
        contactNumber: employee.contactNumber,
        address: employee.address,
        username: employee.username,
        department: employee.department,
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

export const toggleEmployeeStatus = async (req, res) => {
  try {
    const { employeeId } = req.params

    const employee = await Employee.findById(employeeId)
    if (!employee) {
      return res.status(404).json({
        success: false,
        message: "Employee not found",
      })
    }

    employee.isActive = !employee.isActive
    await employee.save()

    res.json({
      success: true,
      message: `Employee ${employee.isActive ? "activated" : "deactivated"} successfully`,
      employee: {
        id: employee._id,
        name: employee.name,
        isActive: employee.isActive,
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}
