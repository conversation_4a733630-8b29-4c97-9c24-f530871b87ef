"use client"

import { useState, useEffect } from "react"
import { useAuth } from "../contexts/AuthContext"
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "../components/ui/Card"
import { Button } from "../components/ui/Button"
import { Badge } from "../components/ui/Badge"
import { useToast } from "../components/ui/Toast"
import AdminManagement from "../components/AdminManagement"
import DepartmentManagement from "../components/DepartmentManagement"
import ProjectManagement from "../components/ProjectManagement"
import TaskTypeManagement from "../components/TaskTypeManagement"
import { Users, Building2, FolderOpen, BarChart3, Settings, UserPlus, Plus, Tags } from "lucide-react"

const SuperAdminDashboard = () => {
  const { user } = useAuth()
  const { toast } = useToast()
  const [stats, setStats] = useState({
    totalAdmins: 0,
    totalDepartments: 0,
    totalProjects: 0,
    totalEmployees: 0,
  })
  const [loading, setLoading] = useState(true)
  const [showAdminManagement, setShowAdminManagement] = useState(false)
  const [showDepartmentManagement, setShowDepartmentManagement] = useState(false)
  const [showProjectManagement, setShowProjectManagement] = useState(false)
  const [showTaskTypeManagement, setShowTaskTypeManagement] = useState(false)

  // Check if user is read-write superadmin
  const isReadWriteSuperAdmin = user?.role === "superadmin" && user?.permissions === "read-write"

  useEffect(() => {
    fetchDashboardStats()
  }, [])

  const fetchDashboardStats = async () => {
    try {
      const token = localStorage.getItem("token")

      // Fetch stats from multiple endpoints
      const [adminsRes, departmentsRes, projectsRes, employeesRes] = await Promise.all([
        fetch("http://localhost:5000/api/admin/all", {
          headers: { Authorization: `Bearer ${token}` },
        }),
        fetch("http://localhost:5000/api/department/all", {
          headers: { Authorization: `Bearer ${token}` },
        }),
        fetch("http://localhost:5000/api/project/all", {
          headers: { Authorization: `Bearer ${token}` },
        }),
        fetch("http://localhost:5000/api/employee/all", {
          headers: { Authorization: `Bearer ${token}` },
        }),
      ])

      const [adminsData, departmentsData, projectsData, employeesData] = await Promise.all([
        adminsRes.json(),
        departmentsRes.json(),
        projectsRes.json(),
        employeesRes.json(),
      ])

      setStats({
        totalAdmins: adminsData.success ? adminsData.data.length : 0,
        totalDepartments: departmentsData.success ? departmentsData.data.length : 0,
        totalProjects: projectsData.success ? projectsData.data.length : 0,
        totalEmployees: employeesData.success ? employeesData.data.length : 0,
      })
    } catch (error) {
      console.error("Error fetching dashboard stats:", error)
      toast.error({ title: "Error", description: "Failed to load dashboard statistics" })
    } finally {
      setLoading(false)
    }
  }

  const StatCard = ({ title, value, icon: Icon, color }) => (
    <Card className="hover:shadow-lg transition-shadow">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
            <p className="text-3xl font-bold text-gray-900 dark:text-white">{loading ? "..." : value}</p>
          </div>
          <div className={`p-3 rounded-full ${color}`}>
            <Icon className="h-6 w-6 text-white" />
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Super Admin Dashboard</h1>
              <p className="text-gray-600 dark:text-gray-400 mt-2">Welcome back, {user?.name}</p>
              <div className="flex items-center gap-2 mt-2">
                <Badge
                  variant="outline"
                  className="bg-purple-100 text-purple-800 border-purple-300 dark:bg-purple-900 dark:text-purple-200"
                >
                  Super Admin
                </Badge>
                <Badge
                  variant="outline"
                  className={
                    isReadWriteSuperAdmin
                      ? "bg-green-100 text-green-800 border-green-300 dark:bg-green-900 dark:text-green-200"
                      : "bg-blue-100 text-blue-800 border-blue-300 dark:bg-blue-900 dark:text-blue-200"
                  }
                >
                  {user?.permissions === "read-write" ? "Read-Write" : "Read-Only"}
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard title="Total Admins" value={stats.totalAdmins} icon={Users} color="bg-blue-500" />
          <StatCard title="Total Departments" value={stats.totalDepartments} icon={Building2} color="bg-green-500" />
          <StatCard title="Total Projects" value={stats.totalProjects} icon={FolderOpen} color="bg-purple-500" />
          <StatCard title="Total Employees" value={stats.totalEmployees} icon={BarChart3} color="bg-orange-500" />
        </div>

        {/* Management Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Admin Management */}
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Admin Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Manage system administrators and their permissions
              </p>
              <Button
                onClick={() => setShowAdminManagement(true)}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                disabled={!isReadWriteSuperAdmin}
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Manage Admins
              </Button>
              {!isReadWriteSuperAdmin && <p className="text-sm text-gray-500 mt-2">Read-only access - cannot modify</p>}
            </CardContent>
          </Card>

          {/* Department Management */}
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Department Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 dark:text-gray-400 mb-4">Create and manage organizational departments</p>
              <Button
                onClick={() => setShowDepartmentManagement(true)}
                className="w-full bg-green-600 hover:bg-green-700 text-white"
                disabled={!isReadWriteSuperAdmin}
              >
                <Plus className="h-4 w-4 mr-2" />
                Manage Departments
              </Button>
              {!isReadWriteSuperAdmin && <p className="text-sm text-gray-500 mt-2">Read-only access - cannot modify</p>}
            </CardContent>
          </Card>

          {/* Project Management */}
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FolderOpen className="h-5 w-5" />
                Project Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 dark:text-gray-400 mb-4">Oversee all projects across the organization</p>
              <Button
                onClick={() => setShowProjectManagement(true)}
                className="w-full bg-purple-600 hover:bg-purple-700 text-white"
              >
                <Settings className="h-4 w-4 mr-2" />
                Manage Projects
              </Button>
            </CardContent>
          </Card>

          {/* Task Type Management */}
          {isReadWriteSuperAdmin && (
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Tags className="h-5 w-5" />
                  Task Type Management
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Create and manage task types for better categorization
                </p>
                <Button
                  onClick={() => setShowTaskTypeManagement(true)}
                  className="w-full bg-indigo-600 hover:bg-indigo-700 text-white"
                >
                  <Tags className="h-4 w-4 mr-2" />
                  Manage Task Types
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* System Information */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>System Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">System Version:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">v1.0.0</span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Last Updated:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">{new Date().toLocaleDateString()}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Status:</span>
                <Badge className="ml-2 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Online</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Management Modals */}
      <AdminManagement isOpen={showAdminManagement} onClose={() => setShowAdminManagement(false)} />
      <DepartmentManagement isOpen={showDepartmentManagement} onClose={() => setShowDepartmentManagement(false)} />
      <ProjectManagement isOpen={showProjectManagement} onClose={() => setShowProjectManagement(false)} />
      <TaskTypeManagement isOpen={showTaskTypeManagement} onClose={() => setShowTaskTypeManagement(false)} />
    </div>
  )
}

export default SuperAdminDashboard
