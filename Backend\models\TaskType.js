const mongoose = require("mongoose")

const taskTypeSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    maxlength: 100,
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
})

// Update the updatedAt field before saving
taskTypeSchema.pre("save", function (next) {
  this.updatedAt = Date.now()
  next()
})

// Create indexes for better performance
taskTypeSchema.index({ name: 1 })
taskTypeSchema.index({ isActive: 1 })
taskTypeSchema.index({ createdBy: 1 })

module.exports = mongoose.model("TaskType", taskTypeSchema)
