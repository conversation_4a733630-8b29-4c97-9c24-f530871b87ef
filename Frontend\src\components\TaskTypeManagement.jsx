"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "./ui/Card"
import { Button } from "./ui/Button"
import { Input } from "./ui/Input"
import { Badge } from "./ui/Badge"
import { useToast } from "./ui/Toast"
import { Plus, Search, Edit, Trash2, ToggleLeft, ToggleRight, X } from "lucide-react"

const TaskTypeManagement = ({ isOpen, onClose }) => {
  const [taskTypes, setTaskTypes] = useState([])
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingTaskType, setEditingTaskType] = useState(null)
  const [formData, setFormData] = useState({
    name: "",
    description: "",
  })
  const [formErrors, setFormErrors] = useState({})
  const { showToast } = useToast()

  // Fetch task types
  const fetchTaskTypes = async () => {
    try {
      setLoading(true)
      const token = localStorage.getItem("token")
      const response = await fetch(`http://localhost:5000/api/tasktype/all?search=${searchTerm}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      const data = await response.json()
      if (data.success) {
        setTaskTypes(data.data)
      } else {
        showToast(data.message || "Failed to fetch task types", "error")
      }
    } catch (error) {
      console.error("Error fetching task types:", error)
      showToast("Failed to fetch task types", "error")
    } finally {
      setLoading(false)
    }
  }

  // Load task types on component mount and search term change
  useEffect(() => {
    if (isOpen) {
      fetchTaskTypes()
    }
  }, [isOpen, searchTerm])

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
    // Clear error for this field
    if (formErrors[name]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: "",
      }))
    }
  }

  // Validate form
  const validateForm = () => {
    const errors = {}

    if (!formData.name.trim()) {
      errors.name = "Task type name is required"
    } else if (formData.name.trim().length > 50) {
      errors.name = "Task type name cannot exceed 50 characters"
    }

    if (formData.description && formData.description.length > 200) {
      errors.description = "Description cannot exceed 200 characters"
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Handle create task type
  const handleCreateTaskType = async (e) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    try {
      setLoading(true)
      const token = localStorage.getItem("token")
      const response = await fetch("http://localhost:5000/api/tasktype/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()
      if (data.success) {
        showToast("Task type created successfully", "success")
        setFormData({ name: "", description: "" })
        setShowCreateForm(false)
        fetchTaskTypes()
      } else {
        showToast(data.message || "Failed to create task type", "error")
      }
    } catch (error) {
      console.error("Error creating task type:", error)
      showToast("Failed to create task type", "error")
    } finally {
      setLoading(false)
    }
  }

  // Handle edit task type
  const handleEditTaskType = async (e) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    try {
      setLoading(true)
      const token = localStorage.getItem("token")
      const response = await fetch(`http://localhost:5000/api/tasktype/${editingTaskType._id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()
      if (data.success) {
        showToast("Task type updated successfully", "success")
        setFormData({ name: "", description: "" })
        setEditingTaskType(null)
        fetchTaskTypes()
      } else {
        showToast(data.message || "Failed to update task type", "error")
      }
    } catch (error) {
      console.error("Error updating task type:", error)
      showToast("Failed to update task type", "error")
    } finally {
      setLoading(false)
    }
  }

  // Handle toggle task type status
  const handleToggleStatus = async (taskTypeId) => {
    try {
      const token = localStorage.getItem("token")
      const response = await fetch(`http://localhost:5000/api/tasktype/${taskTypeId}/toggle-status`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      const data = await response.json()
      if (data.success) {
        showToast(data.message, "success")
        fetchTaskTypes()
      } else {
        showToast(data.message || "Failed to toggle task type status", "error")
      }
    } catch (error) {
      console.error("Error toggling task type status:", error)
      showToast("Failed to toggle task type status", "error")
    }
  }

  // Handle delete task type
  const handleDeleteTaskType = async (taskTypeId, taskTypeName) => {
    if (!window.confirm(`Are you sure you want to delete "${taskTypeName}"? This action cannot be undone.`)) {
      return
    }

    try {
      const token = localStorage.getItem("token")
      const response = await fetch(`http://localhost:5000/api/tasktype/${taskTypeId}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      const data = await response.json()
      if (data.success) {
        showToast("Task type deleted successfully", "success")
        fetchTaskTypes()
      } else {
        showToast(data.message || "Failed to delete task type", "error")
      }
    } catch (error) {
      console.error("Error deleting task type:", error)
      showToast("Failed to delete task type", "error")
    }
  }

  // Start editing
  const startEdit = (taskType) => {
    setEditingTaskType(taskType)
    setFormData({
      name: taskType.name,
      description: taskType.description || "",
    })
    setShowCreateForm(false)
  }

  // Cancel form
  const cancelForm = () => {
    setFormData({ name: "", description: "" })
    setFormErrors({})
    setShowCreateForm(false)
    setEditingTaskType(null)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Task Type Management</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Search and Create Section */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Search task types..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button
              onClick={() => {
                setShowCreateForm(true)
                setEditingTaskType(null)
                setFormData({ name: "", description: "" })
              }}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Task Type
            </Button>
          </div>

          {/* Create/Edit Form */}
          {(showCreateForm || editingTaskType) && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>{editingTaskType ? "Edit Task Type" : "Create New Task Type"}</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={editingTaskType ? handleEditTaskType : handleCreateTaskType}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Task Type Name *
                      </label>
                      <Input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="Enter task type name"
                        className={formErrors.name ? "border-red-500" : ""}
                        maxLength={50}
                      />
                      {formErrors.name && <p className="text-red-500 text-sm mt-1">{formErrors.name}</p>}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Description
                      </label>
                      <Input
                        type="text"
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        placeholder="Enter description (optional)"
                        className={formErrors.description ? "border-red-500" : ""}
                        maxLength={200}
                      />
                      {formErrors.description && <p className="text-red-500 text-sm mt-1">{formErrors.description}</p>}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button type="submit" disabled={loading} className="bg-green-600 hover:bg-green-700 text-white">
                      {loading ? "Saving..." : editingTaskType ? "Update" : "Create"}
                    </Button>
                    <Button type="button" variant="outline" onClick={cancelForm}>
                      Cancel
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          )}

          {/* Task Types List */}
          <div className="space-y-4">
            {loading && taskTypes.length === 0 ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-500 mt-2">Loading task types...</p>
              </div>
            ) : taskTypes.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No task types found</p>
              </div>
            ) : (
              taskTypes.map((taskType) => (
                <Card key={taskType._id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-semibold text-lg text-gray-900 dark:text-white">{taskType.name}</h3>
                          <Badge
                            variant={taskType.isActive ? "success" : "secondary"}
                            className={
                              taskType.isActive
                                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                            }
                          >
                            {taskType.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                        {taskType.description && (
                          <p className="text-gray-600 dark:text-gray-400 mb-2">{taskType.description}</p>
                        )}
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          Created by {taskType.createdBy?.name || "Unknown"} on{" "}
                          {new Date(taskType.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="flex items-center gap-2 ml-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleStatus(taskType._id)}
                          className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900"
                        >
                          {taskType.isActive ? <ToggleRight className="h-4 w-4" /> : <ToggleLeft className="h-4 w-4" />}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => startEdit(taskType)}
                          className="text-yellow-600 hover:text-yellow-700 hover:bg-yellow-50 dark:hover:bg-yellow-900"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteTaskType(taskType._id, taskType.name)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default TaskTypeManagement
