import express from "express"
import {
  createTaskType,
  getAllTaskTypes,
  getTaskTypeById,
  updateTaskType,
  toggleTaskTypeStatus,
  deleteTaskType,
} from "../controllers/taskTypeController.js"
import { verifyToken, adminOrSuperAdmin } from "../middleware/auth.js"

const router = express.Router()

// All routes require authentication and superadmin role
router.use(authenticateToken)
router.use(requireSuperAdmin)

// GET /api/tasktype/all - Get all task types (read-only and read-write can access)
router.get("/all", getAllTaskTypes)

// GET /api/tasktype/:taskTypeId - Get single task type (read-only and read-write can access)
router.get("/:taskTypeId", getTaskTypeById)

// The following routes require read-write permissions
router.use(requireReadWritePermission)

// POST /api/tasktype/create - Create new task type
router.post("/create", createTaskType)

// PUT /api/tasktype/:taskTypeId - Update task type
router.put("/:taskTypeId", updateTaskType)

// PUT /api/tasktype/:taskTypeId/toggle-status - Toggle task type status
router.put("/:taskTypeId/toggle-status", toggleTaskTypeStatus)

// DELETE /api/tasktype/:taskTypeId - Delete task type
router.delete("/:taskTypeId", deleteTaskType)

export default router
