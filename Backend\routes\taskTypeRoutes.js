import express from "express"
import {
  createTaskType,
  getAllTaskTypes,
  getTaskTypeById,
  updateTaskType,
  toggleTaskTypeStatus,
  deleteTaskType,
} from "../controllers/taskTypeController.js"
import { verifyToken, superAdminReadWrite, anySuperAdmin } from "../middleware/auth.js"

const router = express.Router()

// GET /api/tasktype/all - Get all task types (any superadmin can access)
router.get("/all", verifyToken, anySuperAdmin, getAllTaskTypes)

// GET /api/tasktype/:taskTypeId - Get single task type (any superadmin can access)
router.get("/:taskTypeId", verifyToken, anySuperAdmin, getTaskTypeById)

// POST /api/tasktype/create - Create new task type (read-write superadmin only)
router.post("/create", verifyToken, superAdminReadWrite, createTaskType)

// PUT /api/tasktype/:taskTypeId - Update task type (read-write superadmin only)
router.put("/:taskTypeId", verifyToken, superAdminReadWrite, updateTaskType)

// PUT /api/tasktype/:taskTypeId/toggle-status - Toggle task type status (read-write superadmin only)
router.put("/:taskTypeId/toggle-status", verifyToken, superAdminReadWrite, toggleTaskTypeStatus)

// DELETE /api/tasktype/:taskTypeId - Delete task type (read-write superadmin only)
router.delete("/:taskTypeId", verifyToken, superAdminReadWrite, deleteTaskType)

export default router
